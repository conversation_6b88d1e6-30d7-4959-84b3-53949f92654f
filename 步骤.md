# Mind Elixir Core 项目启动步骤

## 项目简介

Mind Elixir 是一个开源的 JavaScript 思维导图核心库，基于 TypeScript 开发，可以与任何前端框架配合使用。

## 环境要求

- **Node.js**: 版本 16.0 或更高
- **包管理器**: pnpm (推荐) 或 npm
- **浏览器**: 支持现代浏览器 (Chrome, Firefox, Safari, Edge)

## 1. 环境准备

### 1.1 安装 Node.js
确保您的系统已安装 Node.js 16.0 或更高版本：
```bash
node --version
```

### 1.2 安装 pnpm (推荐)
```bash
npm install -g pnpm
```

或者使用 npm：
```bash
# 如果选择使用 npm，后续命令中的 pnpm 替换为 npm
```

## 2. 项目设置

### 2.1 克隆项目 (如果从 Git 仓库)
```bash
git clone https://github.com/ssshooter/mind-elixir-core.git
cd mind-elixir-core
```

### 2.2 安装依赖
```bash
pnpm install
```

这将安装所有必要的开发依赖，包括：
- TypeScript 编译器
- Vite 构建工具
- ESLint 代码检查工具
- Playwright 测试框架
- 其他开发工具

## 3. 开发环境启动

### 3.1 启动开发服务器
```bash
pnpm dev
```

这将：
- 启动 Vite 开发服务器
- 在 `http://localhost:23333` 端口运行
- 支持热重载 (Hot Module Replacement)
- 自动打开浏览器 (如果配置)

### 3.2 访问开发环境
打开浏览器访问：
```
http://localhost:23333
```

您将看到思维导图的演示页面，包含两个示例实例。

## 4. 项目构建

### 4.1 构建生产版本
```bash
pnpm build
```

这将：
- 运行自定义构建脚本 (`build.js`)
- 生成多个版本的构建文件：
  - `MindElixir.js` - 完整版本
  - `MindElixirLite.js` - 轻量版本
  - `example.js` - 示例数据
  - `LayoutSsr.js` - 服务端渲染布局
- 编译 TypeScript 类型定义文件

### 4.2 类型检查
```bash
pnpm tsc
```

单独运行 TypeScript 编译器进行类型检查。

### 4.3 预览构建结果
```bash
pnpm preview
```

启动 Vite 预览服务器查看构建后的结果。

## 5. 代码质量检查

### 5.1 运行 ESLint
```bash
pnpm lint
```

这将：
- 检查 `src/**/*.{js,json,ts}` 文件
- 自动修复可修复的问题
- 显示代码质量警告和错误

## 6. 测试

### 6.1 运行端到端测试
```bash
pnpm test
```

使用 Playwright 运行自动化测试，包括：
- 拖拽功能测试
- 交互功能测试
- 多实例测试
- 多节点操作测试
- 主题选择测试

### 6.2 运行测试 UI
```bash
pnpm test:ui
```

启动 Playwright 测试 UI 界面，可视化查看和调试测试。

## 7. 文档生成

### 7.1 生成 API 文档
```bash
pnpm doc
```

使用 Microsoft API Extractor 生成 API 文档。

### 7.2 生成 Markdown 文档
```bash
pnpm doc:md
```

将 API 文档转换为 Markdown 格式。

## 8. 开发工作流

### 8.1 Git Hooks
项目配置了 Husky Git hooks：
- **pre-commit**: 运行 lint-staged 检查代码质量
- **commit-msg**: 使用 commitlint 检查提交信息格式

### 8.2 代码格式化
项目使用 Prettier 和 ESLint 进行代码格式化：
- TypeScript/JavaScript 文件自动修复
- JSON/Less 文件自动格式化

## 9. 项目结构说明

```
mind-elixir-core/
├── src/                    # 源代码目录
│   ├── index.ts           # 主入口文件
│   ├── dev.ts             # 开发环境入口
│   ├── types/             # TypeScript 类型定义
│   ├── utils/             # 工具函数
│   ├── exampleData/       # 示例数据
│   └── ...
├── tests/                 # 测试文件
├── dist/                  # 构建输出目录
├── package.json           # 项目配置
├── vite.config.ts         # Vite 配置
├── tsconfig.json          # TypeScript 配置
├── playwright.config.ts   # Playwright 测试配置
└── build.js              # 自定义构建脚本
```

## 10. 常见问题解决

### 10.1 端口被占用
如果 23333 端口被占用，修改 `vite.config.ts` 中的端口配置。

### 10.2 依赖安装失败
```bash
# 清除缓存重新安装
pnpm store prune
rm -rf node_modules
pnpm install
```

### 10.3 类型检查错误
确保 TypeScript 版本兼容，运行：
```bash
pnpm tsc --noEmit
```

## 11. 下一步

- 查看 `src/dev.ts` 了解如何使用 API
- 阅读 `readme.md` 了解详细功能
- 访问 https://docs.mind-elixir.com/ 查看完整文档
- 查看 `tests/` 目录了解测试用例

现在您可以开始开发和使用 Mind Elixir Core 了！
